package com.example.service.score;

import com.example.common.excel.ExcelConfigFactory;
import com.example.service.excel.UniversalExcelService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 成绩录入服务集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class GradeInputServiceIntegrationTest {

    @Autowired
    private GradeInputService gradeInputService;

    @Autowired
    private UniversalExcelService universalExcelService;

    @Test
    public void testGenerateImportTemplate() {
        // 测试生成导入模板
        byte[] templateData = gradeInputService.generateImportTemplate("CS2021", "CS101", 1);
        
        assertNotNull(templateData);
        assertTrue(templateData.length > 0);
        System.out.println("成绩导入模板生成成功，大小: " + templateData.length + " bytes");
    }

    @Test
    public void testGetImportTemplateHeaders() {
        // 测试获取模板表头
        var headers = gradeInputService.getImportTemplateHeaders();
        
        assertNotNull(headers);
        assertFalse(headers.isEmpty());
        assertTrue(headers.containsKey("studentId"));
        assertTrue(headers.containsKey("finalScore"));
        
        System.out.println("模板表头: " + headers);
    }

    @Test
    public void testUniversalExcelServiceIntegration() throws IOException {
        // 测试通用Excel服务集成
        byte[] templateData = universalExcelService.generateTemplate(ExcelConfigFactory.getGradeImportConfig());
        
        assertNotNull(templateData);
        assertTrue(templateData.length > 0);
        
        // 验证模板和服务生成的模板一致
        byte[] serviceTemplate = gradeInputService.generateImportTemplate("CS2021", "CS101", 1);
        
        // 两个模板应该具有相同的结构（虽然可能不完全相同，但都应该是有效的Excel文件）
        assertTrue(templateData.length > 0);
        assertTrue(serviceTemplate.length > 0);
        
        System.out.println("通用Excel服务集成测试通过");
    }
}
