<template>
  <div class="grade-search-form">
    <el-form :model="searchForm" inline>
      <el-form-item label="学生姓名">
        <el-input
          v-model="searchForm.studentName"
          placeholder="请输入学生姓名"
          clearable
          style="width: 150px"
          @input="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="学号">
        <el-input
          v-model="searchForm.studentId"
          placeholder="请输入学号"
          clearable
          style="width: 150px"
          @input="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="录入状态">
        <el-select
          v-model="searchForm.hasGrade"
          placeholder="请选择录入状态"
          clearable
          style="width: 120px"
          @change="handleSearch"
        >
          <el-option label="已录入" :value="true" />
          <el-option label="未录入" :value="false" />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-space>
          <el-button type="primary" @click="handleSearch">
            <template #icon>
              <IconifyIconOnline icon="ep:search" />
            </template>
            搜索
          </el-button>
          
          <el-button @click="handleReset">
            <template #icon>
              <IconifyIconOnline icon="ep:refresh" />
            </template>
            重置
          </el-button>
          
          <el-button 
            v-if="showAdvanced"
            type="info" 
            @click="toggleAdvanced"
          >
            <template #icon>
              <IconifyIconOnline icon="ep:setting" />
            </template>
            {{ advancedVisible ? '收起' : '高级' }}
          </el-button>
        </el-space>
      </el-form-item>
    </el-form>
    
    <!-- 高级搜索 -->
    <el-collapse-transition>
      <div v-show="advancedVisible" class="advanced-search">
        <el-divider content-position="left">高级搜索</el-divider>
        <el-form :model="advancedForm" inline>
          <el-form-item label="成绩范围">
            <el-input-number
              v-model="advancedForm.minScore"
              placeholder="最低分"
              :min="0"
              :max="100"
              style="width: 100px"
            />
            <span style="margin: 0 8px">-</span>
            <el-input-number
              v-model="advancedForm.maxScore"
              placeholder="最高分"
              :min="0"
              :max="100"
              style="width: 100px"
            />
          </el-form-item>
          
          <el-form-item label="绩点范围">
            <el-input-number
              v-model="advancedForm.minGradePoint"
              placeholder="最低绩点"
              :min="0"
              :max="5"
              :precision="1"
              style="width: 100px"
            />
            <span style="margin: 0 8px">-</span>
            <el-input-number
              v-model="advancedForm.maxGradePoint"
              placeholder="最高绩点"
              :min="0"
              :max="5"
              :precision="1"
              style="width: 100px"
            />
          </el-form-item>
          
          <el-form-item label="是否重修">
            <el-select
              v-model="advancedForm.isRetake"
              placeholder="请选择"
              clearable
              style="width: 100px"
            >
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-space>
              <el-button type="primary" @click="handleAdvancedSearch">
                应用筛选
              </el-button>
              <el-button @click="handleAdvancedReset">
                重置筛选
              </el-button>
            </el-space>
          </el-form-item>
        </el-form>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";

defineOptions({
  name: "GradeSearchForm"
});

// Props
interface Props {
  modelValue: {
    studentName: string;
    studentId: string;
    hasGrade?: boolean;
  };
  showAdvanced?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showAdvanced: true
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: Props["modelValue"]];
  search: [searchParams: any];
  reset: [];
}>();

// 响应式数据
const searchForm = reactive({ ...props.modelValue });
const advancedVisible = ref(false);

// 高级搜索表单
const advancedForm = reactive({
  minScore: undefined as number | undefined,
  maxScore: undefined as number | undefined,
  minGradePoint: undefined as number | undefined,
  maxGradePoint: undefined as number | undefined,
  isRetake: undefined as boolean | undefined
});

// 搜索
const handleSearch = () => {
  emit("update:modelValue", { ...searchForm });
  emit("search", {
    basic: { ...searchForm },
    advanced: { ...advancedForm }
  });
};

// 重置
const handleReset = () => {
  searchForm.studentName = "";
  searchForm.studentId = "";
  searchForm.hasGrade = undefined;
  
  emit("update:modelValue", { ...searchForm });
  emit("reset");
};

// 切换高级搜索
const toggleAdvanced = () => {
  advancedVisible.value = !advancedVisible.value;
};

// 高级搜索
const handleAdvancedSearch = () => {
  emit("search", {
    basic: { ...searchForm },
    advanced: { ...advancedForm }
  });
};

// 重置高级搜索
const handleAdvancedReset = () => {
  advancedForm.minScore = undefined;
  advancedForm.maxScore = undefined;
  advancedForm.minGradePoint = undefined;
  advancedForm.maxGradePoint = undefined;
  advancedForm.isRetake = undefined;
  
  handleAdvancedSearch();
};
</script>

<style scoped>
.grade-search-form {
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.advanced-search {
  margin-top: 16px;
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.el-divider {
  margin: 12px 0;
}
</style>
