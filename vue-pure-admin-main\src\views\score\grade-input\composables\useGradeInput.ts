import { ref, reactive, computed } from "vue";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getStudentGradeInputList,
  inputGrade,
  updateGrade,
  deleteGrade,
  batchInputGrades,
  checkGradeExists,
  type GradeInputVO,
  type GradeInputDTO,
  type BatchGradeInputDTO
} from "@/api/score/grade-input";

export interface GradeSearchForm {
  studentName: string;
  studentId: string;
  hasGrade?: boolean;
}

export interface GradeInputState {
  loading: boolean;
  gradeData: GradeInputVO[];
  searchForm: GradeSearchForm;
  selectedGrades: GradeInputVO[];
}

export function useGradeInput(
  classCode: string,
  courseCode: string,
  semesterId: number
) {
  // 状态管理
  const state = reactive<GradeInputState>({
    loading: false,
    gradeData: [],
    searchForm: {
      studentName: "",
      studentId: "",
      hasGrade: undefined
    },
    selectedGrades: []
  });

  // 计算属性
  const filteredGradeData = computed(() => {
    let data = state.gradeData;
    
    // 按学生姓名筛选
    if (state.searchForm.studentName) {
      data = data.filter(item => 
        item.studentName?.includes(state.searchForm.studentName)
      );
    }
    
    // 按学号筛选
    if (state.searchForm.studentId) {
      data = data.filter(item => 
        item.studentId?.includes(state.searchForm.studentId)
      );
    }
    
    // 按录入状态筛选
    if (state.searchForm.hasGrade !== undefined) {
      data = data.filter(item => item.hasGrade === state.searchForm.hasGrade);
    }
    
    return data;
  });

  const statistics = computed(() => {
    const total = state.gradeData.length;
    const inputted = state.gradeData.filter(item => item.hasGrade).length;
    const notInputted = total - inputted;
    const inputRate = total > 0 ? ((inputted / total) * 100).toFixed(1) : "0";
    
    return {
      total,
      inputted,
      notInputted,
      inputRate
    };
  });

  // 获取成绩数据
  const fetchGradeData = async () => {
    if (!classCode || !courseCode || !semesterId) {
      return;
    }

    state.loading = true;
    try {
      const { data } = await getStudentGradeInputList(classCode, courseCode, semesterId);
      state.gradeData = data || [];
    } catch (error) {
      console.error("获取成绩数据失败:", error);
      message("获取成绩数据失败", { type: "error" });
    } finally {
      state.loading = false;
    }
  };

  // 录入单个成绩
  const inputSingleGrade = async (gradeData: GradeInputDTO) => {
    try {
      if (gradeData.id) {
        await updateGrade(gradeData.id, gradeData);
      } else {
        await inputGrade(gradeData);
      }
      message("操作成功", { type: "success" });
      await fetchGradeData();
      return true;
    } catch (error) {
      console.error("录入成绩失败:", error);
      message("录入成绩失败", { type: "error" });
      return false;
    }
  };

  // 批量录入成绩
  const batchInputGrade = async (grades: GradeInputDTO[]) => {
    try {
      const batchData: BatchGradeInputDTO = {
        classCode,
        courseCode,
        semesterId,
        grades
      };
      await batchInputGrades(batchData);
      message("批量录入成功", { type: "success" });
      await fetchGradeData();
      return true;
    } catch (error) {
      console.error("批量录入失败:", error);
      message("批量录入失败", { type: "error" });
      return false;
    }
  };

  // 删除成绩
  const deleteSingleGrade = async (grade: GradeInputVO) => {
    if (!grade.id) return false;

    try {
      await ElMessageBox.confirm("确定要删除这条成绩记录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      });

      await deleteGrade(grade.id);
      message("删除成功", { type: "success" });
      await fetchGradeData();
      return true;
    } catch (error) {
      if (error !== "cancel") {
        console.error("删除成绩失败:", error);
        message("删除失败", { type: "error" });
      }
      return false;
    }
  };

  // 批量删除成绩
  const batchDeleteGrades = async (grades: GradeInputVO[]) => {
    const gradeIds = grades.filter(g => g.id).map(g => g.id!);
    if (gradeIds.length === 0) return false;

    try {
      await ElMessageBox.confirm(`确定要删除选中的 ${gradeIds.length} 条成绩记录吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      });

      // 并发删除
      await Promise.all(gradeIds.map(id => deleteGrade(id)));
      message("批量删除成功", { type: "success" });
      await fetchGradeData();
      return true;
    } catch (error) {
      if (error !== "cancel") {
        console.error("批量删除失败:", error);
        message("批量删除失败", { type: "error" });
      }
      return false;
    }
  };

  // 检查成绩是否存在
  const checkGradeExistence = async (studentId: string) => {
    try {
      const { data } = await checkGradeExists(studentId, courseCode, semesterId);
      return data;
    } catch (error) {
      console.error("检查成绩失败:", error);
      return false;
    }
  };

  // 重置搜索
  const resetSearch = () => {
    state.searchForm.studentName = "";
    state.searchForm.studentId = "";
    state.searchForm.hasGrade = undefined;
  };

  // 选择成绩
  const selectGrades = (grades: GradeInputVO[]) => {
    state.selectedGrades = grades;
  };

  return {
    state,
    filteredGradeData,
    statistics,
    fetchGradeData,
    inputSingleGrade,
    batchInputGrade,
    deleteSingleGrade,
    batchDeleteGrades,
    checkGradeExistence,
    resetSearch,
    selectGrades
  };
}
