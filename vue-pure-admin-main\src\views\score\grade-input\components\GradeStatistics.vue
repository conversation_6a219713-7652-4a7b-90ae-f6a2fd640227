<template>
  <div class="grade-statistics">
    <!-- 统计卡片 -->
    <el-row :gutter="16" class="statistics-cards">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <IconifyIconOnline icon="ep:user" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.total }}</div>
              <div class="stat-label">总人数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon inputted">
              <IconifyIconOnline icon="ep:check" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.inputted }}</div>
              <div class="stat-label">已录入</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon not-inputted">
              <IconifyIconOnline icon="ep:close" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.notInputted }}</div>
              <div class="stat-label">未录入</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon rate">
              <IconifyIconOnline icon="ep:data-analysis" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.inputRate }}%</div>
              <div class="stat-label">录入率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 成绩分布 -->
    <el-row :gutter="16" class="distribution-section">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>成绩分布</span>
              <el-button type="text" @click="refreshDistribution">
                <IconifyIconOnline icon="ep:refresh" />
              </el-button>
            </div>
          </template>
          
          <div class="distribution-chart">
            <div class="distribution-item" v-for="item in gradeDistribution" :key="item.label">
              <div class="distribution-label">{{ item.label }}</div>
              <div class="distribution-bar">
                <div 
                  class="distribution-fill" 
                  :style="{ 
                    width: `${item.percentage}%`, 
                    backgroundColor: item.color 
                  }"
                ></div>
              </div>
              <div class="distribution-value">{{ item.count }}人 ({{ item.percentage.toFixed(1) }}%)</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>成绩统计</span>
            </div>
          </template>
          
          <div class="score-stats">
            <div class="score-stat-item">
              <span class="score-stat-label">平均分：</span>
              <span class="score-stat-value">{{ scoreStatistics.avgScore }}</span>
            </div>
            <div class="score-stat-item">
              <span class="score-stat-label">最高分：</span>
              <span class="score-stat-value">{{ scoreStatistics.maxScore }}</span>
            </div>
            <div class="score-stat-item">
              <span class="score-stat-label">最低分：</span>
              <span class="score-stat-value">{{ scoreStatistics.minScore }}</span>
            </div>
            <div class="score-stat-item">
              <span class="score-stat-label">平均绩点：</span>
              <span class="score-stat-value">{{ scoreStatistics.avgGradePoint }}</span>
            </div>
            <div class="score-stat-item">
              <span class="score-stat-label">及格率：</span>
              <span class="score-stat-value">{{ scoreStatistics.passRate }}%</span>
            </div>
            <div class="score-stat-item">
              <span class="score-stat-label">优秀率：</span>
              <span class="score-stat-value">{{ scoreStatistics.excellentRate }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";
import type { GradeInputVO } from "@/api/score/grade-input";

defineOptions({
  name: "GradeStatistics"
});

// Props
interface Props {
  data: GradeInputVO[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  refresh: [];
}>();

// 基础统计
const statistics = computed(() => {
  const total = props.data.length;
  const inputted = props.data.filter(item => item.hasGrade).length;
  const notInputted = total - inputted;
  const inputRate = total > 0 ? ((inputted / total) * 100) : 0;
  
  return {
    total,
    inputted,
    notInputted,
    inputRate: inputRate.toFixed(1)
  };
});

// 成绩分布
const gradeDistribution = computed(() => {
  const inputtedGrades = props.data.filter(item => item.hasGrade && item.finalScore !== null);
  const total = inputtedGrades.length;
  
  if (total === 0) {
    return [
      { label: "优秀 (90-100)", count: 0, percentage: 0, color: "#67c23a" },
      { label: "良好 (80-89)", count: 0, percentage: 0, color: "#409eff" },
      { label: "中等 (70-79)", count: 0, percentage: 0, color: "#e6a23c" },
      { label: "及格 (60-69)", count: 0, percentage: 0, color: "#f56c6c" },
      { label: "不及格 (<60)", count: 0, percentage: 0, color: "#909399" }
    ];
  }
  
  const excellent = inputtedGrades.filter(item => item.finalScore! >= 90).length;
  const good = inputtedGrades.filter(item => item.finalScore! >= 80 && item.finalScore! < 90).length;
  const medium = inputtedGrades.filter(item => item.finalScore! >= 70 && item.finalScore! < 80).length;
  const pass = inputtedGrades.filter(item => item.finalScore! >= 60 && item.finalScore! < 70).length;
  const fail = inputtedGrades.filter(item => item.finalScore! < 60).length;
  
  return [
    { label: "优秀 (90-100)", count: excellent, percentage: (excellent / total) * 100, color: "#67c23a" },
    { label: "良好 (80-89)", count: good, percentage: (good / total) * 100, color: "#409eff" },
    { label: "中等 (70-79)", count: medium, percentage: (medium / total) * 100, color: "#e6a23c" },
    { label: "及格 (60-69)", count: pass, percentage: (pass / total) * 100, color: "#f56c6c" },
    { label: "不及格 (<60)", count: fail, percentage: (fail / total) * 100, color: "#909399" }
  ];
});

// 成绩统计
const scoreStatistics = computed(() => {
  const validGrades = props.data.filter(item => item.hasGrade && item.finalScore !== null);
  
  if (validGrades.length === 0) {
    return {
      avgScore: "-",
      maxScore: "-",
      minScore: "-",
      avgGradePoint: "-",
      passRate: "0",
      excellentRate: "0"
    };
  }
  
  const scores = validGrades.map(item => item.finalScore!);
  const gradePoints = validGrades.map(item => item.gradePoint || 0);
  
  const avgScore = (scores.reduce((sum, score) => sum + score, 0) / scores.length).toFixed(1);
  const maxScore = Math.max(...scores).toFixed(1);
  const minScore = Math.min(...scores).toFixed(1);
  const avgGradePoint = (gradePoints.reduce((sum, gp) => sum + gp, 0) / gradePoints.length).toFixed(2);
  
  const passCount = scores.filter(score => score >= 60).length;
  const excellentCount = scores.filter(score => score >= 90).length;
  const passRate = ((passCount / scores.length) * 100).toFixed(1);
  const excellentRate = ((excellentCount / scores.length) * 100).toFixed(1);
  
  return {
    avgScore,
    maxScore,
    minScore,
    avgGradePoint,
    passRate,
    excellentRate
  };
});

// 刷新分布
const refreshDistribution = () => {
  emit("refresh");
};
</script>

<style scoped>
.grade-statistics {
  margin-bottom: 16px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 16px;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.inputted {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.not-inputted {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}

.distribution-section {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.distribution-chart {
  padding: 8px 0;
}

.distribution-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.distribution-label {
  width: 100px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.distribution-bar {
  flex: 1;
  height: 20px;
  background: var(--el-fill-color-light);
  border-radius: 10px;
  margin: 0 12px;
  overflow: hidden;
}

.distribution-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.distribution-value {
  width: 120px;
  text-align: right;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.score-stats {
  padding: 8px 0;
}

.score-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.score-stat-item:last-child {
  border-bottom: none;
}

.score-stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.score-stat-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}
</style>
