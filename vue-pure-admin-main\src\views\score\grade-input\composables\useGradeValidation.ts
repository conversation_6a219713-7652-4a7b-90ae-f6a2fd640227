import { reactive, computed } from "vue";
import type { FormRules } from "element-plus";
import type { GradeInputDTO } from "@/api/score/grade-input";

export interface GradeValidationState {
  errors: Record<string, string>;
  isValid: boolean;
}

export function useGradeValidation() {
  const state = reactive<GradeValidationState>({
    errors: {},
    isValid: true
  });

  // 成绩录入表单验证规则
  const gradeInputRules = computed((): FormRules => ({
    studentId: [
      { required: true, message: "请输入学号", trigger: "blur" },
      { 
        pattern: /^[0-9]{8,12}$/, 
        message: "学号格式不正确，应为8-12位数字", 
        trigger: "blur" 
      }
    ],
    finalScore: [
      { required: true, message: "请输入期末成绩", trigger: "blur" },
      { 
        type: "number", 
        min: 0, 
        max: 100, 
        message: "成绩应在0-100之间", 
        trigger: "blur" 
      }
    ],
    courseCode: [
      { required: true, message: "请选择课程", trigger: "change" }
    ],
    semesterId: [
      { required: true, message: "请选择学期", trigger: "change" }
    ]
  }));

  // 批量录入验证规则
  const batchInputRules = computed((): FormRules => ({
    classCode: [
      { required: true, message: "请选择班级", trigger: "change" }
    ],
    courseCode: [
      { required: true, message: "请选择课程", trigger: "change" }
    ],
    semesterId: [
      { required: true, message: "请选择学期", trigger: "change" }
    ]
  }));

  // 验证单个成绩数据
  const validateGradeData = (gradeData: Partial<GradeInputDTO>): boolean => {
    const errors: Record<string, string> = {};

    // 验证学号
    if (!gradeData.studentId) {
      errors.studentId = "学号不能为空";
    } else if (!/^[0-9]{8,12}$/.test(gradeData.studentId)) {
      errors.studentId = "学号格式不正确，应为8-12位数字";
    }

    // 验证期末成绩
    if (gradeData.finalScore === undefined || gradeData.finalScore === null) {
      errors.finalScore = "期末成绩不能为空";
    } else if (gradeData.finalScore < 0 || gradeData.finalScore > 100) {
      errors.finalScore = "成绩应在0-100之间";
    }

    // 验证课程代码
    if (!gradeData.courseCode) {
      errors.courseCode = "课程代码不能为空";
    }

    // 验证学期ID
    if (!gradeData.semesterId) {
      errors.semesterId = "学期ID不能为空";
    }

    state.errors = errors;
    state.isValid = Object.keys(errors).length === 0;

    return state.isValid;
  };

  // 验证批量成绩数据
  const validateBatchGradeData = (grades: Partial<GradeInputDTO>[]): {
    isValid: boolean;
    errors: Array<{ index: number; field: string; message: string }>;
  } => {
    const errors: Array<{ index: number; field: string; message: string }> = [];

    grades.forEach((grade, index) => {
      // 验证学号
      if (!grade.studentId) {
        errors.push({ index, field: "studentId", message: "学号不能为空" });
      } else if (!/^[0-9]{8,12}$/.test(grade.studentId)) {
        errors.push({ index, field: "studentId", message: "学号格式不正确" });
      }

      // 验证期末成绩
      if (grade.finalScore === undefined || grade.finalScore === null) {
        errors.push({ index, field: "finalScore", message: "期末成绩不能为空" });
      } else if (grade.finalScore < 0 || grade.finalScore > 100) {
        errors.push({ index, field: "finalScore", message: "成绩应在0-100之间" });
      }

      // 验证绩点（如果提供）
      if (grade.gradePoint !== undefined && grade.gradePoint !== null) {
        if (grade.gradePoint < 0 || grade.gradePoint > 5) {
          errors.push({ index, field: "gradePoint", message: "绩点应在0-5之间" });
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  // 计算绩点
  const calculateGradePoint = (score: number): number => {
    if (score >= 95) return 4.0;
    if (score >= 90) return 3.7;
    if (score >= 85) return 3.3;
    if (score >= 80) return 3.0;
    if (score >= 75) return 2.7;
    if (score >= 70) return 2.3;
    if (score >= 65) return 2.0;
    if (score >= 60) return 1.7;
    return 0.0;
  };

  // 验证成绩格式（用于导入）
  const validateScoreFormat = (scoreStr: string): { isValid: boolean; score?: number; message?: string } => {
    if (!scoreStr || scoreStr.trim() === "") {
      return { isValid: false, message: "成绩不能为空" };
    }

    const score = parseFloat(scoreStr.trim());
    
    if (isNaN(score)) {
      return { isValid: false, message: "成绩格式不正确" };
    }

    if (score < 0 || score > 100) {
      return { isValid: false, message: "成绩应在0-100之间" };
    }

    return { isValid: true, score };
  };

  // 清除验证错误
  const clearErrors = () => {
    state.errors = {};
    state.isValid = true;
  };

  // 设置特定字段错误
  const setFieldError = (field: string, message: string) => {
    state.errors[field] = message;
    state.isValid = false;
  };

  // 清除特定字段错误
  const clearFieldError = (field: string) => {
    delete state.errors[field];
    state.isValid = Object.keys(state.errors).length === 0;
  };

  return {
    state,
    gradeInputRules,
    batchInputRules,
    validateGradeData,
    validateBatchGradeData,
    calculateGradePoint,
    validateScoreFormat,
    clearErrors,
    setFieldError,
    clearFieldError
  };
}
