package com.example.common.excel;

import com.example.entity.score.Grade;
import com.example.service.excel.UniversalExcelService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 成绩导入模板测试
 */
@SpringBootTest
public class GradeImportTemplateTest {

    @Test
    public void testGenerateGradeImportTemplate() throws IOException {
        UniversalExcelService excelService = new UniversalExcelService();
        ExcelConfig<Grade> config = ExcelConfigFactory.getGradeImportConfig();
        
        // 生成模板
        byte[] templateData = excelService.generateTemplate(config);
        
        // 保存到文件以便查看
        try (FileOutputStream fos = new FileOutputStream("grade-import-template.xlsx")) {
            fos.write(templateData);
        }
        
        System.out.println("成绩导入模板已生成: grade-import-template.xlsx");
        System.out.println("模板包含字段: 学号, 学生姓名, 课程代码, 课程名称, 期末成绩, 是否重修, 备注");
    }
    
    @Test
    public void testGradeImportTemplateWithSampleData() throws IOException {
        UniversalExcelService excelService = new UniversalExcelService();
        ExcelConfig<Grade> config = ExcelConfigFactory.getGradeImportConfig();
        
        // 创建示例数据
        List<Grade> sampleGrades = new ArrayList<>();
        
        Grade grade1 = new Grade();
        grade1.setStudentId("2021001");
        grade1.setCourseCode("CS101");
        grade1.setFinalScore(new BigDecimal("85.5"));
        grade1.setIsRetake(false);
        grade1.setRemarks("良好");
        sampleGrades.add(grade1);
        
        Grade grade2 = new Grade();
        grade2.setStudentId("2021002");
        grade2.setCourseCode("MATH201");
        grade2.setFinalScore(new BigDecimal("92.0"));
        grade2.setIsRetake(false);
        grade2.setRemarks("优秀");
        sampleGrades.add(grade2);
        
        Grade grade3 = new Grade();
        grade3.setStudentId("2021003");
        grade3.setCourseCode("ENG101");
        grade3.setFinalScore(new BigDecimal("78.0"));
        grade3.setIsRetake(true);
        grade3.setRemarks("重修");
        sampleGrades.add(grade3);
        
        // 导出带示例数据的模板
        byte[] excelData = excelService.exportExcel(sampleGrades, config);
        
        // 保存到文件
        try (FileOutputStream fos = new FileOutputStream("grade-import-template-with-samples.xlsx")) {
            fos.write(excelData);
        }
        
        System.out.println("带示例数据的成绩导入模板已生成: grade-import-template-with-samples.xlsx");
        System.out.println("包含3条示例数据，可以作为填写参考");
    }
    
    @Test
    public void testValidateGradeImportConfig() {
        ExcelConfig<Grade> config = ExcelConfigFactory.getGradeImportConfig();
        
        // 测试配置的基本信息
        String[] headers = config.getFullHeaders();
        System.out.println("导入模板表头:");
        for (int i = 0; i < headers.length; i++) {
            System.out.println((i + 1) + ". " + headers[i]);
        }
        
        // 验证表头内容
        assert headers.length == 7;
        assert "学号".equals(headers[0]);
        assert "学生姓名".equals(headers[1]);
        assert "课程代码".equals(headers[2]);
        assert "课程名称".equals(headers[3]);
        assert "期末成绩".equals(headers[4]);
        assert "是否重修".equals(headers[5]);
        assert "备注".equals(headers[6]);
        
        System.out.println("✓ 成绩导入配置验证通过");
    }
}
