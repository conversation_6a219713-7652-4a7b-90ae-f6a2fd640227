<template>
  <div class="grade-operations">
    <el-space wrap>
      <!-- 基础操作 -->
      <el-button type="info" @click="handleBack">
        <template #icon>
          <IconifyIconOnline icon="ep:back" />
        </template>
        返回
      </el-button>

      <el-button type="success" @click="handleBatchInput">
        <template #icon>
          <IconifyIconOnline icon="ep:edit" />
        </template>
        批量录入
      </el-button>

      <el-button type="warning" @click="handleImport">
        <template #icon>
          <IconifyIconOnline icon="ep:upload" />
        </template>
        导入成绩
      </el-button>

      <!-- 导出操作 -->
      <el-dropdown @command="handleExportCommand">
        <el-button type="primary">
          <template #icon>
            <IconifyIconOnline icon="ep:download" />
          </template>
          导出
          <el-icon class="el-icon--right">
            <IconifyIconOnline icon="ep:arrow-down" />
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="exportData">
              <IconifyIconOnline icon="ep:document" />
              导出成绩数据
            </el-dropdown-item>
            <el-dropdown-item command="exportTemplate">
              <IconifyIconOnline icon="ep:document-copy" />
              导出录入模板
            </el-dropdown-item>
            <el-dropdown-item command="exportStatistics">
              <IconifyIconOnline icon="ep:data-analysis" />
              导出统计报告
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 批量操作（当有选中项时显示） -->
      <template v-if="selectedCount > 0">
        <el-divider direction="vertical" />

        <el-text type="info">
          已选择 {{ selectedCount }} 项
        </el-text>

        <el-button
          type="danger"
          size="small"
          @click="handleBatchDelete"
        >
          <template #icon>
            <IconifyIconOnline icon="ep:delete" />
          </template>
          批量删除
        </el-button>

        <el-button
          type="primary"
          size="small"
          @click="handleBatchEdit"
        >
          <template #icon>
            <IconifyIconOnline icon="ep:edit" />
          </template>
          批量编辑
        </el-button>

        <el-button
          size="small"
          @click="handleClearSelection"
        >
          取消选择
        </el-button>
      </template>

      <!-- 刷新按钮 -->
      <el-button
        circle
        @click="handleRefresh"
        :loading="refreshing"
      >
        <template #icon>
          <IconifyIconOnline icon="ep:refresh" />
        </template>
      </el-button>
    </el-space>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";
import type { GradeInputVO } from "@/api/score/grade-input";

defineOptions({
  name: "GradeOperations"
});

// Props
interface Props {
  selectedItems?: GradeInputVO[];
  refreshing?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  selectedItems: () => [],
  refreshing: false
});

// Emits
const emit = defineEmits<{
  back: [];
  batchInput: [];
  import: [];
  export: [type: "data" | "template" | "statistics"];
  batchDelete: [items: GradeInputVO[]];
  batchEdit: [items: GradeInputVO[]];
  clearSelection: [];
  refresh: [];
}>();

// 计算属性
const selectedCount = computed(() => props.selectedItems.length);

// 返回
const handleBack = () => {
  emit("back");
};

// 批量录入
const handleBatchInput = () => {
  emit("batchInput");
};

// 导入成绩
const handleImport = () => {
  emit("import");
};

// 导出命令处理
const handleExportCommand = (command: string) => {
  switch (command) {
    case "exportData":
      emit("export", "data");
      break;
    case "exportTemplate":
      emit("export", "template");
      break;
    case "exportStatistics":
      emit("export", "statistics");
      break;
  }
};

// 批量删除
const handleBatchDelete = () => {
  if (props.selectedItems.length === 0) return;
  emit("batchDelete", props.selectedItems);
};

// 批量编辑
const handleBatchEdit = () => {
  if (props.selectedItems.length === 0) return;
  emit("batchEdit", props.selectedItems);
};

// 清除选择
const handleClearSelection = () => {
  emit("clearSelection");
};

// 刷新
const handleRefresh = () => {
  emit("refresh");
};
</script>

<style scoped>
.grade-operations {
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  margin-bottom: 16px;
}

.el-divider--vertical {
  height: 20px;
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-dropdown) {
  border-radius: 6px;
}
</style>
