<template>
  <div class="grade-table">
    <pure-table
      ref="tableRef"
      adaptive
      :adaptiveConfig="{ offsetBottom: 108 }"
      align-whole="center"
      table-layout="auto"
      :loading="loading"
      :size="size"
      :data="tableData"
      :columns="dynamicColumns"
      :header-cell-style="{
        background: 'var(--el-fill-color-light)',
        color: 'var(--el-text-color-primary)'
      }"
      @selection-change="handleSelectionChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from "vue";
import type { TableColumnList } from "@pureadmin/table";
import type { GradeInputVO } from "@/api/score/grade-input";

defineOptions({
  name: "GradeTable"
});

// Props
interface Props {
  loading?: boolean;
  data: GradeInputVO[];
  size?: "large" | "default" | "small";
  showSelection?: boolean;
  showIndex?: boolean;
  showOperations?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  size: "default",
  showSelection: false,
  showIndex: true,
  showOperations: true
});

// Emits
const emit = defineEmits<{
  edit: [row: GradeInputVO];
  delete: [row: GradeInputVO];
  selectionChange: [selection: GradeInputVO[]];
}>();

// 响应式数据
const tableRef = ref();

// 表格数据
const tableData = computed(() => props.data);

// 基础列配置
const baseColumns: TableColumnList = [
  {
    label: "学号",
    prop: "studentId",
    width: 120,
    align: "center"
  },
  {
    label: "姓名",
    prop: "studentName",
    width: 100,
    align: "center"
  },
  {
    label: "期末成绩",
    prop: "finalScore",
    width: 100,
    align: "center",
    cellRenderer: ({ row }) => {
      if (row.hasGrade && row.finalScore !== null && row.finalScore !== undefined) {
        const score = row.finalScore;
        let type = "info";
        if (score >= 90) type = "success";
        else if (score >= 80) type = "primary";
        else if (score >= 70) type = "warning";
        else if (score < 60) type = "danger";
        
        return h("el-tag", { type }, score.toFixed(1));
      }
      return h("span", { style: "color: var(--el-text-color-placeholder)" }, "-");
    }
  },
  {
    label: "绩点",
    prop: "gradePoint",
    width: 80,
    align: "center",
    cellRenderer: ({ row }) => {
      if (row.hasGrade && row.gradePoint !== null && row.gradePoint !== undefined) {
        return h("span", { style: "font-weight: 500" }, row.gradePoint.toFixed(2));
      }
      return h("span", { style: "color: var(--el-text-color-placeholder)" }, "-");
    }
  },
  {
    label: "是否重修",
    prop: "isRetake",
    width: 100,
    align: "center",
    cellRenderer: ({ row }) => {
      if (row.hasGrade) {
        return h("el-tag", {
          type: row.isRetake ? "warning" : "success",
          size: "small"
        }, row.isRetake ? "重修" : "正常");
      }
      return h("span", { style: "color: var(--el-text-color-placeholder)" }, "-");
    }
  },
  {
    label: "录入状态",
    prop: "hasGrade",
    width: 100,
    align: "center",
    cellRenderer: ({ row }) => {
      return h("el-tag", {
        type: row.hasGrade ? "success" : "info",
        size: "small"
      }, row.hasGrade ? "已录入" : "未录入");
    }
  },
  {
    label: "备注",
    prop: "remarks",
    minWidth: 120,
    align: "center",
    cellRenderer: ({ row }) => {
      return row.remarks || h("span", { style: "color: var(--el-text-color-placeholder)" }, "-");
    }
  }
];

// 动态列配置
const dynamicColumns = computed((): TableColumnList => {
  const columns: TableColumnList = [];

  // 选择列
  if (props.showSelection) {
    columns.push({
      type: "selection",
      width: 55,
      align: "center"
    });
  }

  // 序号列
  if (props.showIndex) {
    columns.push({
      label: "序号",
      type: "index",
      width: 70,
      align: "center"
    });
  }

  // 基础列
  columns.push(...baseColumns);

  // 操作列
  if (props.showOperations) {
    columns.push({
      label: "操作",
      fixed: "right",
      width: 160,
      align: "center",
      cellRenderer: ({ row }) => {
        const buttons = [
          h("el-button", {
            type: "primary",
            size: "small",
            onClick: () => handleEdit(row)
          }, row.hasGrade ? "编辑" : "录入")
        ];

        if (row.hasGrade) {
          buttons.push(
            h("el-button", {
              type: "danger",
              size: "small",
              onClick: () => handleDelete(row)
            }, "删除")
          );
        }

        return h("el-space", {}, buttons);
      }
    });
  }

  return columns;
});

// 编辑
const handleEdit = (row: GradeInputVO) => {
  emit("edit", row);
};

// 删除
const handleDelete = (row: GradeInputVO) => {
  emit("delete", row);
};

// 选择变化
const handleSelectionChange = (selection: GradeInputVO[]) => {
  emit("selectionChange", selection);
};

// 清除选择
const clearSelection = () => {
  tableRef.value?.clearSelection();
};

// 切换所有选择
const toggleAllSelection = () => {
  tableRef.value?.toggleAllSelection();
};

// 切换行选择
const toggleRowSelection = (row: GradeInputVO, selected?: boolean) => {
  tableRef.value?.toggleRowSelection(row, selected);
};

// 暴露方法
defineExpose({
  clearSelection,
  toggleAllSelection,
  toggleRowSelection
});
</script>

<style scoped>
.grade-table {
  background: var(--el-bg-color);
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table) {
  border-radius: 6px;
}

:deep(.el-table__header) {
  border-radius: 6px 6px 0 0;
}

:deep(.el-table__body) {
  border-radius: 0 0 6px 6px;
}

:deep(.el-tag) {
  border-radius: 4px;
}
</style>
