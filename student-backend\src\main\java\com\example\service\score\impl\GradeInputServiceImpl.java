package com.example.service.score.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.common.PageResult;
import com.example.common.exception.BusinessException;
import com.example.common.excel.ApachePoiExportService;
import com.example.common.excel.ExcelConfigFactory;
import com.example.common.excel.ExcelConfig;
import com.example.dto.score.GradeInputDTO;
import com.example.dto.score.GradeInputQueryDTO;
import com.example.dto.score.BatchGradeInputDTO;
import com.example.dto.score.GradeImportRequestDTO;
import com.example.dto.score.GradeImportResultDTO;
import com.example.entity.score.Grade;
import com.example.mapper.score.GradeInputMapper;
import com.example.service.score.GradeInputService;
import com.example.service.student.StudentsService;
import com.example.service.educational.CourseService;
import com.example.service.basic.ClassesService;
import com.example.service.basic.SemesterService;
import com.example.vo.score.GradeInputVO;
import com.example.vo.student.StudentsVO;
import com.example.vo.educational.CourseVO;
import com.example.vo.basic.ClassesVO;
import com.example.vo.basic.SemesterVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 成绩录入服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GradeInputServiceImpl implements GradeInputService {

    private final GradeInputMapper gradeInputMapper;
    private final ApachePoiExportService apachePoiExportService;
    private final StudentsService studentsService;
    private final CourseService courseService;
    private final ClassesService classesService;
    private final SemesterService semesterService;
    private final Validator validator;

    @Override
    public PageResult<GradeInputVO> getGradeInputPage(GradeInputQueryDTO params) {
        // 设置默认分页参数
        if (params.getCurrent() == null || params.getCurrent() <= 0) {
            params.setCurrent(1);
        }
        if (params.getSize() == null || params.getSize() <= 0) {
            params.setSize(10);
        }

        Page<GradeInputVO> page = new Page<>(params.getCurrent(), params.getSize());
        IPage<GradeInputVO> result = gradeInputMapper.selectGradeInputPage(page, params);

        return PageResult.<GradeInputVO>builder()
                .list(result.getRecords())
                .total(result.getTotal())
                .pageNum(Math.toIntExact(result.getCurrent()))
                .pageSize(Math.toIntExact(result.getSize()))
                .totalPages(result.getPages())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inputGrade(GradeInputDTO gradeInputDTO) {
        // 参数校验
        validateGradeInput(gradeInputDTO);

        // 检查成绩是否已存在
        if (checkGradeExists(gradeInputDTO.getStudentId(), gradeInputDTO.getCourseCode(), gradeInputDTO.getSemesterId())) {
            throw new BusinessException("该学生在此学期的课程成绩已存在，请使用更新功能");
        }

        // 转换为实体
        Grade grade = new Grade();
        BeanUtils.copyProperties(gradeInputDTO, grade);

        // 自动计算绩点（无论是否传入绩点都重新计算）
        if (gradeInputDTO.getFinalScore() != null) {
            Double gradePoint = calculateGradePoint(gradeInputDTO.getFinalScore().doubleValue());
            grade.setGradePoint(BigDecimal.valueOf(gradePoint));
        }

        // 插入数据
        int result = gradeInputMapper.insert(grade);
        log.info("录入成绩成功: studentId={}, courseCode={}, semesterId={}, finalScore={}",
                gradeInputDTO.getStudentId(), gradeInputDTO.getCourseCode(),
                gradeInputDTO.getSemesterId(), gradeInputDTO.getFinalScore());

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInputGrades(BatchGradeInputDTO batchGradeInputDTO) {
        if (batchGradeInputDTO.getGrades() == null || batchGradeInputDTO.getGrades().isEmpty()) {
            throw new BusinessException("成绩列表不能为空");
        }

        int successCount = 0;
        for (GradeInputDTO gradeInputDTO : batchGradeInputDTO.getGrades()) {
            try {
                // 设置班级、课程、学期信息
                gradeInputDTO.setCourseCode(batchGradeInputDTO.getCourseCode());
                gradeInputDTO.setSemesterId(batchGradeInputDTO.getSemesterId());

                // 检查是否已存在，如果存在则更新，否则插入
                if (checkGradeExists(gradeInputDTO.getStudentId(), gradeInputDTO.getCourseCode(), gradeInputDTO.getSemesterId())) {
                    // 获取现有成绩ID
                    Grade existingGrade = gradeInputMapper.selectGradeByCondition(
                            gradeInputDTO.getStudentId(),
                            gradeInputDTO.getCourseCode(),
                            gradeInputDTO.getSemesterId()
                    );
                    if (existingGrade != null) {
                        gradeInputDTO.setId(existingGrade.getId());
                        if (updateGrade(gradeInputDTO)) {
                            successCount++;
                        }
                    }
                } else {
                    if (inputGrade(gradeInputDTO)) {
                        successCount++;
                    }
                }
            } catch (Exception e) {
                log.error("批量录入成绩失败: studentId={}, error={}", gradeInputDTO.getStudentId(), e.getMessage());
            }
        }

        log.info("批量录入成绩完成: 总数={}, 成功={}", batchGradeInputDTO.getGrades().size(), successCount);
        return successCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateGrade(GradeInputDTO gradeInputDTO) {
        if (gradeInputDTO.getId() == null) {
            throw new BusinessException("成绩ID不能为空");
        }

        // 参数校验
        validateGradeInput(gradeInputDTO);

        // 转换为实体
        Grade grade = new Grade();
        BeanUtils.copyProperties(gradeInputDTO, grade);

        // 自动计算绩点（无论是否传入绩点都重新计算）
        if (gradeInputDTO.getFinalScore() != null) {
            Double gradePoint = calculateGradePoint(gradeInputDTO.getFinalScore().doubleValue());
            grade.setGradePoint(BigDecimal.valueOf(gradePoint));
        }

        // 更新数据
        int result = gradeInputMapper.updateById(grade);
        log.info("更新成绩成功: id={}, studentId={}, courseCode={}, finalScore={}",
                gradeInputDTO.getId(), gradeInputDTO.getStudentId(),
                gradeInputDTO.getCourseCode(), gradeInputDTO.getFinalScore());

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteGrade(Integer id) {
        if (id == null) {
            throw new BusinessException("成绩ID不能为空");
        }

        int result = gradeInputMapper.deleteById(id);
        log.info("删除成绩成功: id={}", id);

        return result > 0;
    }

    @Override
    public List<GradeInputVO> getStudentGradeInputList(String classCode, String courseCode, Integer semesterId) {
        if (!StringUtils.hasText(classCode)) {
            throw new BusinessException("班级代码不能为空");
        }
        if (!StringUtils.hasText(courseCode)) {
            throw new BusinessException("课程代码不能为空");
        }
        if (semesterId == null) {
            throw new BusinessException("学期ID不能为空");
        }

        return gradeInputMapper.selectStudentGradeInputList(classCode, courseCode, semesterId);
    }

    @Override
    public GradeInputVO getGradeById(Integer id) {
        if (id == null) {
            throw new BusinessException("成绩ID不能为空");
        }

        return gradeInputMapper.selectGradeInputById(id);
    }

    @Override
    public boolean checkGradeExists(String studentId, String courseCode, Integer semesterId) {
        if (!StringUtils.hasText(studentId) || !StringUtils.hasText(courseCode) || semesterId == null) {
            return false;
        }

        int count = gradeInputMapper.checkGradeExists(studentId, courseCode, semesterId);
        return count > 0;
    }

    /**
     * 计算绩点（私有方法）
     * 规则：
     * - 成绩小于60分：绩点为0.0
     * - 成绩60分及以上：使用公式 (final_score / 10) - 5
     * - 最高绩点为5.0（100分）
     */
    private Double calculateGradePoint(Double finalScore) {
        if (finalScore == null || finalScore < 0 || finalScore > 100) {
            return 0.0;
        }

        // 成绩小于60分，绩点为0
        if (finalScore < 60.0) {
            return 0.0;
        }

        // 成绩60分及以上，使用公式：(final_score / 10) - 5
        double gradePoint = (finalScore / 10.0) - 5.0;

        // 确保绩点不大于5.0（理论上100分对应5.0绩点）
        if (gradePoint > 5.0) {
            gradePoint = 5.0;
        }

        // 保留2位小数
        return BigDecimal.valueOf(gradePoint).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 校验成绩录入参数
     */
    private void validateGradeInput(GradeInputDTO gradeInputDTO) {
        if (!StringUtils.hasText(gradeInputDTO.getStudentId())) {
            throw new BusinessException("学号不能为空");
        }
        if (!StringUtils.hasText(gradeInputDTO.getCourseCode())) {
            throw new BusinessException("课程代码不能为空");
        }
        if (gradeInputDTO.getSemesterId() == null) {
            throw new BusinessException("学期ID不能为空");
        }
        // 允许期末成绩为空，但如果不为空则需要在有效范围内
        if (gradeInputDTO.getFinalScore() != null) {
            if (gradeInputDTO.getFinalScore().compareTo(BigDecimal.ZERO) < 0 ||
                gradeInputDTO.getFinalScore().compareTo(BigDecimal.valueOf(100)) > 0) {
                throw new BusinessException("期末成绩必须在0-100之间");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GradeImportResultDTO importGrades(MultipartFile file, String classCode, String courseCode, Integer semesterId) {
        log.info("开始导入成绩: classCode={}, courseCode={}, semesterId={}, fileName={}",
                classCode, courseCode, semesterId, file.getOriginalFilename());

        GradeImportResultDTO result = new GradeImportResultDTO();

        try {
            // 使用流式Excel服务导入数据
            ApachePoiExportService.ImportResult<Grade> importResult =
                apachePoiExportService.importExcel(file, ExcelConfigFactory.getGradeImportConfig());

            if (!importResult.isSuccess()) {
                result.setSuccess(false);
                result.addErrorMessage(importResult.getMessage());
                return result;
            }

            List<Grade> grades = importResult.getSuccessData();
            result.setTotalRows(grades.size() + importResult.getErrorCount());

            // 设置课程和学期信息，并保存成绩
            int successCount = 0;
            for (Grade grade : grades) {
                try {
                    // 设置课程代码和学期ID
                    grade.setCourseCode(courseCode);
                    grade.setSemesterId(semesterId);

                    // 清除classCode字段，因为它不存储在数据库中
                    grade.setClassCode(null);

                    // 自动计算绩点
                    if (grade.getFinalScore() != null) {
                        Double gradePoint = calculateGradePoint(grade.getFinalScore().doubleValue());
                        grade.setGradePoint(BigDecimal.valueOf(gradePoint));
                    }

                    // 检查成绩是否已存在，如果存在则更新，否则插入
                    if (checkGradeExists(grade.getStudentId(), courseCode, semesterId)) {
                        // 获取现有成绩
                        Grade existingGrade = gradeInputMapper.selectGradeByCondition(
                                grade.getStudentId(), courseCode, semesterId);
                        if (existingGrade != null) {
                            // 更新现有成绩
                            grade.setId(existingGrade.getId());
                            int updateResult = gradeInputMapper.updateById(grade);
                            if (updateResult > 0) {
                                successCount++;
                                log.info("更新成绩成功: studentId={}", grade.getStudentId());
                            } else {
                                result.addErrorDetail(0, grade.getStudentId(), "数据库更新失败");
                            }
                        } else {
                            result.addErrorDetail(0, grade.getStudentId(), "查询现有成绩失败");
                        }
                    } else {
                        // 插入新成绩
                        int insertResult = gradeInputMapper.insert(grade);
                        if (insertResult > 0) {
                            successCount++;
                            log.info("插入成绩成功: studentId={}", grade.getStudentId());
                        } else {
                            result.addErrorDetail(0, grade.getStudentId(), "数据库保存失败");
                        }
                    }

                } catch (Exception e) {
                    log.warn("保存成绩失败: studentId={}, error={}", grade.getStudentId(), e.getMessage());
                    result.addErrorDetail(0, grade.getStudentId(), "保存失败: " + e.getMessage());
                }
            }

            // 添加Excel解析错误
            for (ApachePoiExportService.ImportError error : importResult.getErrors()) {
                result.addErrorDetail(error.getRowNumber(), "", error.getErrorMessage());
            }

            result.setSuccessRows(successCount);
            result.setFailedRows(result.getTotalRows() - successCount);
            result.setSuccess(successCount > 0);

            log.info("成绩导入完成: 总数={}, 成功={}, 失败={}",
                    result.getTotalRows(), result.getSuccessRows(), result.getFailedRows());
            return result;

        } catch (Exception e) {
            log.error("导入成绩失败", e);
            result.setSuccess(false);
            result.addErrorMessage("导入失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public byte[] generateImportTemplate(String classCode, String courseCode, Integer semesterId) {
        log.info("生成成绩导入模板: classCode={}, courseCode={}, semesterId={}", classCode, courseCode, semesterId);

        try {
            // 参数验证
            if (!StringUtils.hasText(classCode)) {
                throw new BusinessException("班级代码不能为空");
            }
            if (!StringUtils.hasText(courseCode)) {
                throw new BusinessException("课程代码不能为空");
            }
            if (semesterId == null) {
                throw new BusinessException("学期ID不能为空");
            }

            // 查询班级学生列表
            List<StudentsVO> students = studentsService.getStudentsByClassCode(classCode);
            if (students.isEmpty()) {
                log.warn("班级 {} 没有找到学生", classCode);
                // 如果没有学生，生成空模板
                return apachePoiExportService.generateTemplate(ExcelConfigFactory.getGradeImportConfig());
            }

            // 查询班级信息
            ClassesVO classInfo = classesService.getClassesByCode(classCode);
            if (classInfo == null) {
                throw new BusinessException("班级不存在: " + classCode);
            }

            // 查询课程信息
            CourseVO course = courseService.getCourseByCode(courseCode);
            if (course == null) {
                throw new BusinessException("课程不存在: " + courseCode);
            }

            // 查询学期信息
            SemesterVO semester = semesterService.getSemesterById(semesterId);
            if (semester == null) {
                throw new BusinessException("学期不存在: " + semesterId);
            }

            // 为每个学生创建预填充的成绩数据
            List<Grade> prefilledGrades = new ArrayList<>();
            for (StudentsVO student : students) {
                Grade grade = new Grade();
                grade.setStudentId(student.getStudentId());
                grade.setClassCode(classCode); // 仅用于Excel模板显示，不存储到数据库
                grade.setCourseCode(courseCode);
                grade.setSemesterId(semesterId);
                // 期末成绩、是否重修、备注留空，由老师填写
                grade.setIsRetake(false); // 默认值
                prefilledGrades.add(grade);
            }

            // 使用预填充数据生成模板
            return generatePrefilledTemplate(prefilledGrades, students, classInfo, course, semester);

        } catch (Exception e) {
            log.error("生成成绩导入模板失败", e);
            throw new BusinessException("生成模板失败: " + e.getMessage());
        }
    }

    /**
     * 生成预填充的Excel模板
     */
    private byte[] generatePrefilledTemplate(List<Grade> grades, List<StudentsVO> students,
                                           ClassesVO classInfo, CourseVO course, SemesterVO semester) {
        try {
            // 构建表头映射
            Map<String, String> headers = new LinkedHashMap<>();
            headers.put("studentId", "学号");
            headers.put("studentName", "学生姓名");
            headers.put("classCode", "班级代码");
            headers.put("className", "班级名称");
            headers.put("courseCode", "课程代码");
            headers.put("courseName", "课程名称");
            headers.put("semesterName", "学期");
            headers.put("finalScore", "期末成绩");
            headers.put("isRetake", "是否重修");
            headers.put("remarks", "备注");

            // 构建字段顺序
            List<String> fieldOrder = new ArrayList<>(headers.keySet());

            // 构建预填充数据
            List<Map<String, Object>> templateData = new ArrayList<>();

            if (!grades.isEmpty()) {
                // 如果有现有成绩数据，使用现有数据
                for (Grade grade : grades) {
                    Map<String, Object> row = new LinkedHashMap<>();
                    row.put("studentId", grade.getStudentId());
                    row.put("studentName", ""); // 需要从学生信息中获取
                    row.put("classCode", grade.getClassCode());
                    row.put("className", classInfo.getClassName());
                    row.put("courseCode", grade.getCourseCode());
                    row.put("courseName", course.getCourseName());
                    row.put("semesterName", semester.getSemesterName());
                    row.put("finalScore", grade.getFinalScore());
                    row.put("isRetake", grade.getIsRetake() != null && grade.getIsRetake() ? "是" : "否");
                    row.put("remarks", grade.getRemarks());
                    templateData.add(row);
                }
            } else {
                // 如果没有现有数据，为每个学生创建空记录
                for (StudentsVO student : students) {
                    Map<String, Object> row = new LinkedHashMap<>();
                    row.put("studentId", student.getStudentId());
                    row.put("studentName", student.getName());
                    row.put("classCode", classInfo.getClassCode());
                    row.put("className", classInfo.getClassName());
                    row.put("courseCode", course.getCourseCode());
                    row.put("courseName", course.getCourseName());
                    row.put("semesterName", semester.getSemesterName());
                    row.put("finalScore", ""); // 期末成绩留空
                    row.put("isRetake", "否");
                    row.put("remarks", ""); // 备注留空
                    templateData.add(row);
                }
            }

            // 使用基于Map的导出方法生成预填充模板
            String sheetName = "成绩导入";
            return apachePoiExportService.exportGradesToExcel(templateData, headers, sheetName);

        } catch (Exception e) {
            log.error("生成预填充模板失败", e);
            throw new BusinessException("生成预填充模板失败: " + e.getMessage());
        }
    }

    @Override
    public String generateTemplateFileName(String classCode, String courseCode, Integer semesterId) {
        try {
            // 查询班级信息
            ClassesVO classInfo = classesService.getClassesByCode(classCode);
            String className = (classInfo != null) ? classInfo.getClassName() : classCode;

            // 查询课程信息
            CourseVO course = courseService.getCourseByCode(courseCode);
            String courseName = (course != null) ? course.getCourseName() : courseCode;

            // 查询学期信息
            SemesterVO semester = semesterService.getSemesterById(semesterId);
            String semesterName = (semester != null) ? semester.getSemesterName() : "学期" + semesterId;

            // 生成文件名：xx学期xx班xx课程成绩导入模板
            return String.format("%s%s%s成绩导入模板.xlsx", semesterName, className, courseName);

        } catch (Exception e) {
            log.warn("生成文件名失败，使用默认文件名: {}", e.getMessage());
            // 如果查询失败，使用默认格式
            return String.format("学期%d班级%s课程%s成绩导入模板.xlsx", semesterId, classCode, courseCode);
        }
    }

    @Override
    public Map<String, String> getImportTemplateHeaders() {
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("studentId", "学号");
        headers.put("courseCode", "课程代码");
        headers.put("semesterId", "学期ID");
        headers.put("finalScore", "期末成绩");
        headers.put("isRetake", "是否重修");
        headers.put("remarks", "备注");
        return headers;
    }

    /**
     * 转换Excel数据为导入DTO
     */
    private List<GradeImportRequestDTO> convertToImportDTOs(List<Map<String, Object>> dataList,
                                                           String classCode, String courseCode, Integer semesterId) {
        List<GradeImportRequestDTO> importDTOs = new ArrayList<>();

        for (int i = 0; i < dataList.size(); i++) {
            Map<String, Object> rowData = dataList.get(i);
            GradeImportRequestDTO dto = new GradeImportRequestDTO();
            dto.setRowNumber(i + 2); // Excel行号从2开始（第1行是表头）

            try {
                // 学号
                Object studentIdObj = rowData.get("studentId");
                if (studentIdObj != null) {
                    dto.setStudentId(studentIdObj.toString().trim());
                }

                // 课程代码（优先使用Excel中的值，如果为空则使用参数值）
                Object courseCodeObj = rowData.get("courseCode");
                if (courseCodeObj != null && !courseCodeObj.toString().trim().isEmpty()) {
                    dto.setCourseCode(courseCodeObj.toString().trim());
                } else {
                    dto.setCourseCode(courseCode);
                }

                // 学期ID（优先使用Excel中的值，如果为空则使用参数值）
                Object semesterIdObj = rowData.get("semesterId");
                if (semesterIdObj != null) {
                    if (semesterIdObj instanceof Number) {
                        dto.setSemesterId(((Number) semesterIdObj).intValue());
                    } else {
                        dto.setSemesterId(Integer.parseInt(semesterIdObj.toString().trim()));
                    }
                } else {
                    dto.setSemesterId(semesterId);
                }

                // 期末成绩
                Object finalScoreObj = rowData.get("finalScore");
                if (finalScoreObj != null) {
                    if (finalScoreObj instanceof Number) {
                        dto.setFinalScore(BigDecimal.valueOf(((Number) finalScoreObj).doubleValue()));
                    } else {
                        dto.setFinalScore(new BigDecimal(finalScoreObj.toString().trim()));
                    }
                }

                // 是否重修
                Object isRetakeObj = rowData.get("isRetake");
                if (isRetakeObj != null) {
                    if (isRetakeObj instanceof Boolean) {
                        dto.setIsRetake((Boolean) isRetakeObj);
                    } else {
                        String retakeStr = isRetakeObj.toString().trim().toLowerCase();
                        dto.setIsRetake("true".equals(retakeStr) || "是".equals(retakeStr) || "1".equals(retakeStr));
                    }
                } else {
                    dto.setIsRetake(false);
                }

                // 备注
                Object remarksObj = rowData.get("remarks");
                if (remarksObj != null) {
                    dto.setRemarks(remarksObj.toString().trim());
                }

                importDTOs.add(dto);

            } catch (Exception e) {
                log.warn("转换第{}行数据失败: {}", i + 2, e.getMessage());
                // 仍然添加到列表中，让验证阶段处理错误
                importDTOs.add(dto);
            }
        }

        return importDTOs;
    }

    /**
     * 验证导入数据
     */
    private List<GradeImportRequestDTO> validateImportData(List<GradeImportRequestDTO> importDTOs, GradeImportResultDTO result) {
        List<GradeImportRequestDTO> validDTOs = new ArrayList<>();

        for (GradeImportRequestDTO dto : importDTOs) {
            List<String> errors = new ArrayList<>();

            // 使用Bean Validation验证
            Set<ConstraintViolation<GradeImportRequestDTO>> violations = validator.validate(dto);
            for (ConstraintViolation<GradeImportRequestDTO> violation : violations) {
                errors.add(violation.getMessage());
            }

            // 检查成绩是否已存在
            if (StringUtils.hasText(dto.getStudentId()) && StringUtils.hasText(dto.getCourseCode()) && dto.getSemesterId() != null) {
                if (checkGradeExists(dto.getStudentId(), dto.getCourseCode(), dto.getSemesterId())) {
                    errors.add("该学生在此学期的课程成绩已存在");
                }
            }

            if (errors.isEmpty()) {
                validDTOs.add(dto);
            } else {
                String errorMessage = String.join("; ", errors);
                result.addErrorDetail(dto.getRowNumber(), dto.getStudentId(), errorMessage);
            }
        }

        return validDTOs;
    }

    /**
     * 批量导入有效成绩
     */
    private int batchImportValidGrades(List<GradeImportRequestDTO> validDTOs, GradeImportResultDTO result) {
        int successCount = 0;

        for (GradeImportRequestDTO dto : validDTOs) {
            try {
                // 转换为Grade实体
                Grade grade = new Grade();
                grade.setStudentId(dto.getStudentId());
                grade.setCourseCode(dto.getCourseCode());
                grade.setSemesterId(dto.getSemesterId());
                grade.setFinalScore(dto.getFinalScore());
                grade.setIsRetake(dto.getIsRetake());
                grade.setRemarks(dto.getRemarks());

                // 自动计算绩点
                if (dto.getFinalScore() != null) {
                    Double gradePoint = calculateGradePoint(dto.getFinalScore().doubleValue());
                    grade.setGradePoint(BigDecimal.valueOf(gradePoint));
                }

                // 插入数据
                int insertResult = gradeInputMapper.insert(grade);
                if (insertResult > 0) {
                    successCount++;
                } else {
                    result.addErrorDetail(dto.getRowNumber(), dto.getStudentId(), "数据库插入失败");
                }

            } catch (Exception e) {
                log.error("导入第{}行成绩失败: {}", dto.getRowNumber(), e.getMessage());
                result.addErrorDetail(dto.getRowNumber(), dto.getStudentId(), "导入失败: " + e.getMessage());
            }
        }

        return successCount;
    }
}
