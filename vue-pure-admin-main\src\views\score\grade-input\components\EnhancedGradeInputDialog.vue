<template>
  <ReDialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="validationRules"
      label-width="120px"
      label-position="right"
      @submit.prevent="handleSubmit"
    >
      <!-- 学生信息 -->
      <el-card shadow="never" class="info-card">
        <template #header>
          <span class="card-title">学生信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="学号" prop="studentId">
              <el-input v-model="form.studentId" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学生姓名">
              <el-input v-model="form.studentName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="班级">
              <el-input v-model="form.className" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专业">
              <el-input v-model="form.majorName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 课程信息 -->
      <el-card shadow="never" class="info-card">
        <template #header>
          <span class="card-title">课程信息</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="课程代码">
              <el-input v-model="form.courseCode" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课程名称">
              <el-input v-model="form.courseName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="学分">
              <el-input v-model="form.credits" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学期">
              <el-input v-model="form.semesterName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 成绩录入 -->
      <el-card shadow="never" class="grade-card">
        <template #header>
          <span class="card-title">成绩录入</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="期末成绩" prop="finalScore">
              <el-input-number
                v-model="form.finalScore"
                :min="0"
                :max="100"
                :precision="1"
                :step="0.1"
                placeholder="请输入期末成绩"
                style="width: 100%"
                @change="handleScoreChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="绩点">
              <el-input
                v-model="calculatedGradePoint"
                placeholder="自动计算"
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="是否重修">
              <el-switch 
                v-model="form.isRetake"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成绩等级">
              <el-tag :type="gradeLevel.type" size="large">
                {{ gradeLevel.text }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '录入' }}
        </el-button>
      </div>
    </template>
  </ReDialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { ReDialog } from "@/components/ReDialog";
import { useGradeValidation } from "../composables/useGradeValidation";
import type { FormInstance } from "element-plus";
import type { GradeInputVO, GradeInputDTO } from "@/api/score/grade-input";

defineOptions({
  name: "EnhancedGradeInputDialog"
});

// Props
interface Props {
  modelValue: boolean;
  gradeData?: GradeInputVO | null;
  courseCode: string;
  courseName: string;
  semesterId: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  gradeData: null,
  courseCode: "",
  courseName: "",
  semesterId: 0
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [data: GradeInputDTO];
}>();

// 使用验证composable
const { gradeInputRules, calculateGradePoint } = useGradeValidation();

// 响应式数据
const formRef = ref<FormInstance>();
const submitting = ref(false);

// 表单数据
const form = reactive<GradeInputDTO & {
  studentName?: string;
  className?: string;
  majorName?: string;
  courseName?: string;
  semesterName?: string;
  credits?: number;
}>({
  id: undefined,
  studentId: "",
  courseCode: props.courseCode,
  semesterId: props.semesterId,
  finalScore: 0,
  gradePoint: 0,
  isRetake: false,
  remarks: "",
  // 显示字段
  studentName: "",
  className: "",
  majorName: "",
  courseName: props.courseName,
  semesterName: "",
  credits: 0
});

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value)
});

const dialogTitle = computed(() => {
  return props.gradeData?.hasGrade ? "编辑成绩" : "录入成绩";
});

const isEdit = computed(() => {
  return props.gradeData?.hasGrade || false;
});

const calculatedGradePoint = computed(() => {
  if (form.finalScore !== null && form.finalScore !== undefined) {
    return calculateGradePoint(form.finalScore).toFixed(2);
  }
  return "";
});

const gradeLevel = computed(() => {
  const score = form.finalScore;
  if (score === null || score === undefined) {
    return { type: "info", text: "-" };
  }
  
  if (score >= 90) return { type: "success", text: "优秀" };
  if (score >= 80) return { type: "primary", text: "良好" };
  if (score >= 70) return { type: "warning", text: "中等" };
  if (score >= 60) return { type: "danger", text: "及格" };
  return { type: "danger", text: "不及格" };
});

const validationRules = computed(() => gradeInputRules.value);

// 监听成绩变化
const handleScoreChange = (value: number | null) => {
  if (value !== null && value !== undefined) {
    form.gradePoint = calculateGradePoint(value);
  }
};

// 监听弹窗打开
watch(visible, (newVal) => {
  if (newVal && props.gradeData) {
    nextTick(() => {
      initForm();
    });
  }
});

// 初始化表单
const initForm = () => {
  if (props.gradeData) {
    Object.assign(form, {
      id: props.gradeData.id,
      studentId: props.gradeData.studentId,
      courseCode: props.gradeData.courseCode,
      semesterId: props.gradeData.semesterId,
      finalScore: props.gradeData.finalScore || 0,
      gradePoint: props.gradeData.gradePoint || 0,
      isRetake: props.gradeData.isRetake || false,
      remarks: props.gradeData.remarks || "",
      // 显示字段
      studentName: props.gradeData.studentName || "",
      className: props.gradeData.className || "",
      majorName: props.gradeData.majorName || "",
      courseName: props.gradeData.courseName || props.courseName,
      semesterName: props.gradeData.semesterName || "",
      credits: props.gradeData.credits || 0
    });
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    
    submitting.value = true;
    
    // 准备提交数据
    const submitData: GradeInputDTO = {
      id: form.id,
      studentId: form.studentId,
      courseCode: form.courseCode,
      semesterId: form.semesterId,
      finalScore: form.finalScore!,
      gradePoint: form.gradePoint,
      isRetake: form.isRetake,
      remarks: form.remarks
    };

    emit("success", submitData);
    handleClose();
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    submitting.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  formRef.value?.resetFields();
  emit("update:modelValue", false);
};
</script>

<style scoped>
.info-card,
.grade-card {
  margin-bottom: 16px;
}

.info-card :deep(.el-card__body) {
  padding: 16px;
}

.grade-card :deep(.el-card__body) {
  padding: 16px;
}

.card-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-tag) {
  font-weight: 500;
}
</style>
