<template>
  <div class="grade-input-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        {{ majorName }} - {{ className }} - {{ courseName }} - {{ semesterName }} - 成绩录入
      </h2>
    </div>

    <!-- 成绩统计 -->
    <GradeStatistics
      :data="gradeInput.filteredGradeData.value"
      @refresh="handleRefresh"
    />

    <!-- 搜索表单 -->
    <GradeSearchForm
      v-model="gradeInput.state.searchForm"
      @search="handleSearch"
      @reset="handleSearchReset"
    />

    <!-- 操作按钮 -->
    <GradeOperations
      :selected-items="selectedGrades"
      :refreshing="gradeInput.state.loading"
      @back="handleBack"
      @batch-input="handleBatchInput"
      @import="handleImport"
      @export="handleExport"
      @batch-delete="handleBatchDelete"
      @batch-edit="handleBatchEdit"
      @clear-selection="handleClearSelection"
      @refresh="handleRefresh"
    />

    <!-- 成绩表格 -->
    <GradeTable
      ref="gradeTableRef"
      :loading="gradeInput.state.loading"
      :data="gradeInput.filteredGradeData.value"
      :show-selection="true"
      @edit="handleEdit"
      @delete="handleDelete"
      @selection-change="handleSelectionChange"
    />

    <!-- 成绩录入弹窗 -->
    <GradeInputDialog
      v-model="inputDialogVisible"
      :grade-data="currentGrade"
      :course-code="courseCode"
      :course-name="courseName"
      :semester-id="semesterId"
      @success="handleInputSuccess"
    />

    <!-- 批量录入弹窗 -->
    <BatchInputDialog
      v-model="batchDialogVisible"
      :class-code="classCode"
      :class-name="className"
      :course-code="courseCode"
      :course-name="courseName"
      :semester-id="semesterId"
      @success="handleBatchSuccess"
    />

    <!-- 导入成绩弹窗 -->
    <ImportDialog
      v-model="importDialogVisible"
      :class-code="classCode"
      :class-name="className"
      :course-code="courseCode"
      :course-name="courseName"
      :semester-id="semesterId"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { useGradeInput } from "../composables/useGradeInput";
import { useGradeExport } from "../composables/useGradeExport";
import GradeStatistics from "./GradeStatistics.vue";
import GradeSearchForm from "./GradeSearchForm.vue";
import GradeOperations from "./GradeOperations.vue";
import GradeTable from "./GradeTable.vue";
import GradeInputDialog from "./GradeInputDialog.vue";
import BatchInputDialog from "./BatchInputDialog.vue";
import ImportDialog from "./ImportDialog.vue";

import type { GradeInputVO } from "@/api/score/grade-input";

defineOptions({
  name: "GradeInputList"
});

// Props
interface Props {
  classCode: string;
  className: string;
  majorName: string;
  courseCode: string;
  courseName: string;
  semesterId: number;
  semesterName: string;
}

const props = withDefaults(defineProps<Props>(), {
  classCode: "",
  className: "",
  majorName: "",
  courseCode: "",
  courseName: "",
  semesterId: 0,
  semesterName: ""
});

// Emits
const emit = defineEmits<{
  back: [];
}>();

// 使用composables
const gradeInput = useGradeInput(props.classCode, props.courseCode, props.semesterId);
const gradeExport = useGradeExport();

// 响应式数据
const gradeTableRef = ref();
const selectedGrades = ref<GradeInputVO[]>([]);

// 弹窗控制
const inputDialogVisible = ref(false);
const batchDialogVisible = ref(false);
const importDialogVisible = ref(false);
const currentGrade = ref<GradeInputVO | null>(null);

// 搜索处理
const handleSearch = (searchParams: any) => {
  // 搜索逻辑已在composable中处理，这里可以添加额外的搜索逻辑
  console.log("搜索参数:", searchParams);
};

// 重置搜索
const handleSearchReset = () => {
  gradeInput.resetSearch();
};

// 刷新数据
const handleRefresh = () => {
  gradeInput.fetchGradeData();
};

// 返回
const handleBack = () => {
  emit('back');
};

// 编辑/录入成绩
const handleEdit = (row: GradeInputVO) => {
  currentGrade.value = row;
  inputDialogVisible.value = true;
};

// 删除成绩
const handleDelete = async (row: GradeInputVO) => {
  await gradeInput.deleteSingleGrade(row);
};

// 批量删除
const handleBatchDelete = async (items: GradeInputVO[]) => {
  await gradeInput.batchDeleteGrades(items);
  selectedGrades.value = [];
};

// 批量编辑
const handleBatchEdit = (items: GradeInputVO[]) => {
  // 可以实现批量编辑逻辑
  message("批量编辑功能开发中", { type: "info" });
};

// 选择变化
const handleSelectionChange = (selection: GradeInputVO[]) => {
  selectedGrades.value = selection;
};

// 清除选择
const handleClearSelection = () => {
  gradeTableRef.value?.clearSelection();
  selectedGrades.value = [];
};

// 导出处理
const handleExport = async (type: "data" | "template" | "statistics") => {
  const courseInfo = {
    courseCode: props.courseCode,
    courseName: props.courseName,
    className: props.className,
    semesterName: props.semesterName
  };

  switch (type) {
    case "data":
      await gradeExport.exportGradeData(gradeInput.filteredGradeData.value, {
        format: "xlsx",
        includeStatistics: true
      });
      break;
    case "template":
      const studentList = gradeInput.state.gradeData.map(item => ({
        studentId: item.studentId,
        studentName: item.studentName || ""
      }));
      await gradeExport.exportGradeTemplate(studentList, courseInfo);
      break;
    case "statistics":
      await gradeExport.exportStatisticsReport(gradeInput.filteredGradeData.value, courseInfo);
      break;
  }
};

// 批量录入
const handleBatchInput = () => {
  batchDialogVisible.value = true;
};

// 导入成绩
const handleImport = () => {
  importDialogVisible.value = true;
};

// 录入成功回调
const handleInputSuccess = () => {
  message("操作成功", { type: "success" });
  gradeInput.fetchGradeData();
};

// 批量录入成功回调
const handleBatchSuccess = () => {
  message("批量录入成功", { type: "success" });
  gradeInput.fetchGradeData();
};

// 导入成功回调
const handleImportSuccess = () => {
  message("导入成功", { type: "success" });
  gradeInput.fetchGradeData();
};

// 组件挂载
onMounted(() => {
  gradeInput.fetchGradeData();
});
</script>

<style scoped>
.grade-input-container {
  padding: 16px;
  background: var(--el-bg-color-page);
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
  padding: 16px 0;
  border-bottom: 2px solid var(--el-color-primary);
  display: inline-block;
}
</style>