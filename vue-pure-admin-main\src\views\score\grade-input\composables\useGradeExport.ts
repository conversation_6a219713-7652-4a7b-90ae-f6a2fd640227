import { ref } from "vue";
import { message } from "@/utils/message";
import type { GradeInputVO } from "@/api/score/grade-input";

export interface ExportOptions {
  format: "xlsx" | "csv";
  includeStatistics: boolean;
  filename?: string;
}

export function useGradeExport() {
  const exporting = ref(false);

  // 导出成绩数据
  const exportGradeData = async (
    data: GradeInputVO[],
    options: ExportOptions = { format: "xlsx", includeStatistics: false }
  ) => {
    if (!data || data.length === 0) {
      message("没有数据可导出", { type: "warning" });
      return;
    }

    exporting.value = true;
    try {
      // 动态导入导出库
      const { utils, writeFile } = await import("xlsx");

      // 准备导出数据
      const exportData = data.map((item, index) => ({
        序号: index + 1,
        学号: item.studentId,
        姓名: item.studentName || "",
        班级: item.className || "",
        课程代码: item.courseCode,
        课程名称: item.courseName || "",
        学期: item.semesterName || "",
        期末成绩: item.hasGrade ? item.finalScore : "",
        绩点: item.hasGrade ? item.gradePoint : "",
        是否重修: item.hasGrade ? (item.isRetake ? "是" : "否") : "",
        录入状态: item.hasGrade ? "已录入" : "未录入",
        备注: item.remarks || "",
        录入时间: item.createdAt || "",
        更新时间: item.updatedAt || ""
      }));

      // 创建工作簿
      const workbook = utils.book_new();
      
      // 添加成绩数据工作表
      const worksheet = utils.json_to_sheet(exportData);
      utils.book_append_sheet(workbook, worksheet, "成绩数据");

      // 如果包含统计信息，添加统计工作表
      if (options.includeStatistics) {
        const total = data.length;
        const inputted = data.filter(item => item.hasGrade).length;
        const notInputted = total - inputted;
        const inputRate = total > 0 ? ((inputted / total) * 100).toFixed(1) : "0";

        const statisticsData = [
          { 统计项目: "总人数", 数值: total },
          { 统计项目: "已录入", 数值: inputted },
          { 统计项目: "未录入", 数值: notInputted },
          { 统计项目: "录入率", 数值: `${inputRate}%` }
        ];

        const statsWorksheet = utils.json_to_sheet(statisticsData);
        utils.book_append_sheet(workbook, statsWorksheet, "统计信息");
      }

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
      const filename = options.filename || `成绩录入数据_${timestamp}.${options.format}`;

      // 导出文件
      writeFile(workbook, filename);
      message("导出成功", { type: "success" });
    } catch (error) {
      console.error("导出失败:", error);
      message("导出失败", { type: "error" });
    } finally {
      exporting.value = false;
    }
  };

  // 导出成绩模板
  const exportGradeTemplate = async (
    studentList: Array<{ studentId: string; studentName: string }>,
    courseInfo: { courseCode: string; courseName: string; semesterName: string }
  ) => {
    exporting.value = true;
    try {
      const { utils, writeFile } = await import("xlsx");

      // 准备模板数据
      const templateData = studentList.map((student, index) => ({
        序号: index + 1,
        学号: student.studentId,
        姓名: student.studentName,
        课程代码: courseInfo.courseCode,
        课程名称: courseInfo.courseName,
        学期: courseInfo.semesterName,
        期末成绩: "", // 空白，供填写
        是否重修: "否", // 默认值
        备注: ""
      }));

      // 创建工作簿
      const workbook = utils.book_new();
      const worksheet = utils.json_to_sheet(templateData);
      
      // 设置列宽
      const colWidths = [
        { wch: 6 },  // 序号
        { wch: 12 }, // 学号
        { wch: 10 }, // 姓名
        { wch: 12 }, // 课程代码
        { wch: 20 }, // 课程名称
        { wch: 12 }, // 学期
        { wch: 10 }, // 期末成绩
        { wch: 10 }, // 是否重修
        { wch: 20 }  // 备注
      ];
      worksheet["!cols"] = colWidths;

      utils.book_append_sheet(workbook, worksheet, "成绩录入模板");

      // 添加说明工作表
      const instructionData = [
        { 说明: "1. 期末成绩：请填写0-100之间的数字" },
        { 说明: "2. 是否重修：请填写"是"或"否"" },
        { 说明: "3. 请勿修改学号、姓名、课程代码等基础信息" },
        { 说明: "4. 导入时请确保数据格式正确" },
        { 说明: "5. 空白成绩将被忽略，不会覆盖已有成绩" }
      ];
      const instructionWorksheet = utils.json_to_sheet(instructionData);
      utils.book_append_sheet(workbook, instructionWorksheet, "填写说明");

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 10);
      const filename = `成绩录入模板_${courseInfo.courseName}_${timestamp}.xlsx`;

      // 导出文件
      writeFile(workbook, filename);
      message("模板导出成功", { type: "success" });
    } catch (error) {
      console.error("模板导出失败:", error);
      message("模板导出失败", { type: "error" });
    } finally {
      exporting.value = false;
    }
  };

  // 导出成绩统计报告
  const exportStatisticsReport = async (
    data: GradeInputVO[],
    courseInfo: { courseCode: string; courseName: string; className: string; semesterName: string }
  ) => {
    exporting.value = true;
    try {
      const { utils, writeFile } = await import("xlsx");

      // 基础统计
      const total = data.length;
      const inputted = data.filter(item => item.hasGrade).length;
      const notInputted = total - inputted;
      const inputRate = total > 0 ? ((inputted / total) * 100).toFixed(1) : "0";

      // 成绩分布统计
      const gradeDistribution = {
        excellent: data.filter(item => item.hasGrade && item.finalScore! >= 90).length,
        good: data.filter(item => item.hasGrade && item.finalScore! >= 80 && item.finalScore! < 90).length,
        medium: data.filter(item => item.hasGrade && item.finalScore! >= 70 && item.finalScore! < 80).length,
        pass: data.filter(item => item.hasGrade && item.finalScore! >= 60 && item.finalScore! < 70).length,
        fail: data.filter(item => item.hasGrade && item.finalScore! < 60).length
      };

      // 平均分和绩点
      const validGrades = data.filter(item => item.hasGrade && item.finalScore !== null);
      const avgScore = validGrades.length > 0 
        ? (validGrades.reduce((sum, item) => sum + item.finalScore!, 0) / validGrades.length).toFixed(2)
        : "0";
      const avgGradePoint = validGrades.length > 0
        ? (validGrades.reduce((sum, item) => sum + (item.gradePoint || 0), 0) / validGrades.length).toFixed(2)
        : "0";

      // 创建工作簿
      const workbook = utils.book_new();

      // 基础统计工作表
      const basicStats = [
        { 统计项目: "课程信息", 数值: `${courseInfo.courseName} (${courseInfo.courseCode})` },
        { 统计项目: "班级", 数值: courseInfo.className },
        { 统计项目: "学期", 数值: courseInfo.semesterName },
        { 统计项目: "总人数", 数值: total },
        { 统计项目: "已录入", 数值: inputted },
        { 统计项目: "未录入", 数值: notInputted },
        { 统计项目: "录入率", 数值: `${inputRate}%` },
        { 统计项目: "平均分", 数值: avgScore },
        { 统计项目: "平均绩点", 数值: avgGradePoint }
      ];
      const basicStatsSheet = utils.json_to_sheet(basicStats);
      utils.book_append_sheet(workbook, basicStatsSheet, "基础统计");

      // 成绩分布工作表
      const distributionStats = [
        { 等级: "优秀 (90-100)", 人数: gradeDistribution.excellent, 占比: `${((gradeDistribution.excellent / inputted) * 100).toFixed(1)}%` },
        { 等级: "良好 (80-89)", 人数: gradeDistribution.good, 占比: `${((gradeDistribution.good / inputted) * 100).toFixed(1)}%` },
        { 等级: "中等 (70-79)", 人数: gradeDistribution.medium, 占比: `${((gradeDistribution.medium / inputted) * 100).toFixed(1)}%` },
        { 等级: "及格 (60-69)", 人数: gradeDistribution.pass, 占比: `${((gradeDistribution.pass / inputted) * 100).toFixed(1)}%` },
        { 等级: "不及格 (<60)", 人数: gradeDistribution.fail, 占比: `${((gradeDistribution.fail / inputted) * 100).toFixed(1)}%` }
      ];
      const distributionSheet = utils.json_to_sheet(distributionStats);
      utils.book_append_sheet(workbook, distributionSheet, "成绩分布");

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 10);
      const filename = `成绩统计报告_${courseInfo.courseName}_${courseInfo.className}_${timestamp}.xlsx`;

      // 导出文件
      writeFile(workbook, filename);
      message("统计报告导出成功", { type: "success" });
    } catch (error) {
      console.error("统计报告导出失败:", error);
      message("统计报告导出失败", { type: "error" });
    } finally {
      exporting.value = false;
    }
  };

  return {
    exporting,
    exportGradeData,
    exportGradeTemplate,
    exportStatisticsReport
  };
}
