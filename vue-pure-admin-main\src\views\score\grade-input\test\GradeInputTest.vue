<template>
  <div class="test-container">
    <h2>成绩录入功能测试</h2>
    
    <!-- 测试按钮 -->
    <el-space wrap>
      <el-button type="primary" @click="testComposables">
        测试Composables
      </el-button>
      <el-button type="success" @click="testComponents">
        测试组件
      </el-button>
      <el-button type="warning" @click="testExport">
        测试导出功能
      </el-button>
      <el-button type="info" @click="testValidation">
        测试验证功能
      </el-button>
    </el-space>

    <!-- 测试结果 -->
    <el-card class="test-results" v-if="testResults.length > 0">
      <template #header>
        <span>测试结果</span>
        <el-button type="text" @click="clearResults">清除</el-button>
      </template>
      
      <div v-for="(result, index) in testResults" :key="index" class="test-result-item">
        <el-tag :type="result.success ? 'success' : 'danger'">
          {{ result.success ? '✓' : '✗' }}
        </el-tag>
        <span class="test-name">{{ result.name }}</span>
        <span class="test-message">{{ result.message }}</span>
      </div>
    </el-card>

    <!-- 组件演示 -->
    <el-row :gutter="16" class="demo-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>搜索表单组件</span>
          </template>
          <GradeSearchForm
            v-model="searchForm"
            @search="handleSearch"
            @reset="handleReset"
          />
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>操作按钮组件</span>
          </template>
          <GradeOperations
            :selected-items="selectedItems"
            @back="handleBack"
            @batch-input="handleBatchInput"
            @export="handleExport"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计组件演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>统计组件</span>
      </template>
      <GradeStatistics :data="mockGradeData" />
    </el-card>

    <!-- 表格组件演示 -->
    <el-card class="demo-section">
      <template #header>
        <span>表格组件</span>
      </template>
      <GradeTable
        :data="mockGradeData"
        :show-selection="true"
        @edit="handleEdit"
        @delete="handleDelete"
        @selection-change="handleSelectionChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { useGradeInput } from "../composables/useGradeInput";
import { useGradeValidation } from "../composables/useGradeValidation";
import { useGradeExport } from "../composables/useGradeExport";
import GradeSearchForm from "../components/GradeSearchForm.vue";
import GradeOperations from "../components/GradeOperations.vue";
import GradeStatistics from "../components/GradeStatistics.vue";
import GradeTable from "../components/GradeTable.vue";
import type { GradeInputVO } from "@/api/score/grade-input";

defineOptions({
  name: "GradeInputTest"
});

// 测试结果
interface TestResult {
  name: string;
  success: boolean;
  message: string;
}

const testResults = ref<TestResult[]>([]);

// 模拟数据
const mockGradeData = ref<GradeInputVO[]>([
  {
    id: 1,
    studentId: "20210001",
    studentName: "张三",
    classCode: "CS2021",
    className: "计算机科学2021级",
    courseCode: "CS101",
    courseName: "数据结构",
    semesterId: 1,
    semesterName: "2023-2024学年第一学期",
    finalScore: 85,
    gradePoint: 3.3,
    isRetake: false,
    hasGrade: true,
    remarks: "表现良好"
  },
  {
    id: 2,
    studentId: "20210002",
    studentName: "李四",
    classCode: "CS2021",
    className: "计算机科学2021级",
    courseCode: "CS101",
    courseName: "数据结构",
    semesterId: 1,
    semesterName: "2023-2024学年第一学期",
    finalScore: null,
    gradePoint: null,
    isRetake: false,
    hasGrade: false,
    remarks: ""
  }
]);

const searchForm = reactive({
  studentName: "",
  studentId: "",
  hasGrade: undefined
});

const selectedItems = ref<GradeInputVO[]>([]);

// 测试Composables
const testComposables = () => {
  try {
    // 测试useGradeInput
    const gradeInput = useGradeInput("CS2021", "CS101", 1);
    addTestResult("useGradeInput", true, "成功创建useGradeInput实例");

    // 测试useGradeValidation
    const validation = useGradeValidation();
    const testGrade = {
      studentId: "20210001",
      courseCode: "CS101",
      semesterId: 1,
      finalScore: 85
    };
    const isValid = validation.validateGradeData(testGrade);
    addTestResult("useGradeValidation", isValid, isValid ? "验证通过" : "验证失败");

    // 测试useGradeExport
    const gradeExport = useGradeExport();
    addTestResult("useGradeExport", true, "成功创建useGradeExport实例");

  } catch (error) {
    addTestResult("Composables测试", false, `错误: ${error}`);
  }
};

// 测试组件
const testComponents = () => {
  try {
    addTestResult("组件加载", true, "所有组件成功加载");
  } catch (error) {
    addTestResult("组件测试", false, `错误: ${error}`);
  }
};

// 测试导出功能
const testExport = async () => {
  try {
    const gradeExport = useGradeExport();
    // 这里只是测试函数是否存在，不实际导出
    if (typeof gradeExport.exportGradeData === 'function') {
      addTestResult("导出功能", true, "导出函数可用");
    } else {
      addTestResult("导出功能", false, "导出函数不可用");
    }
  } catch (error) {
    addTestResult("导出测试", false, `错误: ${error}`);
  }
};

// 测试验证功能
const testValidation = () => {
  try {
    const validation = useGradeValidation();
    
    // 测试有效数据
    const validData = {
      studentId: "20210001",
      courseCode: "CS101",
      semesterId: 1,
      finalScore: 85
    };
    const isValid = validation.validateGradeData(validData);
    addTestResult("有效数据验证", isValid, isValid ? "验证通过" : "验证失败");

    // 测试无效数据
    const invalidData = {
      studentId: "",
      courseCode: "",
      semesterId: 0,
      finalScore: 150
    };
    const isInvalid = !validation.validateGradeData(invalidData);
    addTestResult("无效数据验证", isInvalid, isInvalid ? "正确识别无效数据" : "未能识别无效数据");

    // 测试绩点计算
    const gradePoint = validation.calculateGradePoint(85);
    addTestResult("绩点计算", gradePoint === 3.3, `85分对应绩点: ${gradePoint}`);

  } catch (error) {
    addTestResult("验证测试", false, `错误: ${error}`);
  }
};

// 添加测试结果
const addTestResult = (name: string, success: boolean, message: string) => {
  testResults.value.push({ name, success, message });
};

// 清除测试结果
const clearResults = () => {
  testResults.value = [];
};

// 事件处理
const handleSearch = (params: any) => {
  ElMessage.success("搜索功能触发");
  console.log("搜索参数:", params);
};

const handleReset = () => {
  ElMessage.info("重置功能触发");
};

const handleBack = () => {
  ElMessage.info("返回功能触发");
};

const handleBatchInput = () => {
  ElMessage.info("批量录入功能触发");
};

const handleExport = (type: string) => {
  ElMessage.success(`导出功能触发: ${type}`);
};

const handleEdit = (row: GradeInputVO) => {
  ElMessage.info(`编辑学生: ${row.studentName}`);
};

const handleDelete = (row: GradeInputVO) => {
  ElMessage.warning(`删除学生: ${row.studentName}`);
};

const handleSelectionChange = (selection: GradeInputVO[]) => {
  selectedItems.value = selection;
  ElMessage.info(`选择了 ${selection.length} 项`);
};
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.test-results {
  margin: 20px 0;
}

.test-result-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 12px;
}

.test-name {
  font-weight: 600;
  min-width: 120px;
}

.test-message {
  color: var(--el-text-color-regular);
}

.demo-section {
  margin: 20px 0;
}
</style>
