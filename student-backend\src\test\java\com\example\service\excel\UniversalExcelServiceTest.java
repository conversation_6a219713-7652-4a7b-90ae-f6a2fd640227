package com.example.service.excel;

import com.example.common.excel.ExcelConfig;
import com.example.common.excel.ExcelConfigFactory;
import com.example.entity.score.Grade;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 通用Excel服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class UniversalExcelServiceTest {

    @Autowired
    private UniversalExcelService universalExcelService;

    @Test
    public void testGenerateTemplate() throws IOException {
        // 测试生成成绩导入模板
        ExcelConfig<Grade> config = ExcelConfigFactory.getGradeImportConfig();
        byte[] templateData = universalExcelService.generateTemplate(config);

        assertNotNull(templateData);
        assertTrue(templateData.length > 0);
        System.out.println("模板生成成功，大小: " + templateData.length + " bytes");
    }

    @Test
    public void testExportExcel() throws IOException {
        // 准备测试数据
        List<Map<String, Object>> testData = new ArrayList<>();

        Map<String, Object> grade1 = new HashMap<>();
        grade1.put("studentId", "2021001");
        grade1.put("studentName", "张三");
        grade1.put("className", "计算机科学与技术1班");
        grade1.put("courseName", "数据结构");
        grade1.put("finalScore", new BigDecimal("85.5"));
        grade1.put("gradePoint", new BigDecimal("3.5"));
        grade1.put("isRetake", false);
        grade1.put("semesterName", "2023-2024学年第一学期");
        grade1.put("remarks", "");
        testData.add(grade1);

        Map<String, Object> grade2 = new HashMap<>();
        grade2.put("studentId", "2021002");
        grade2.put("studentName", "李四");
        grade2.put("className", "计算机科学与技术1班");
        grade2.put("courseName", "数据结构");
        grade2.put("finalScore", new BigDecimal("92.0"));
        grade2.put("gradePoint", new BigDecimal("4.0"));
        grade2.put("isRetake", false);
        grade2.put("semesterName", "2023-2024学年第一学期");
        grade2.put("remarks", "优秀");
        testData.add(grade2);

        // 转换数据类型以匹配泛型
        List<Object> objectData = new ArrayList<>(testData);

        // 测试导出
        ExcelConfig<Object> config = ExcelConfigFactory.getGradeExportConfig();
        byte[] excelData = universalExcelService.exportExcel(objectData, config);

        assertNotNull(excelData);
        assertTrue(excelData.length > 0);
        System.out.println("Excel导出成功，大小: " + excelData.length + " bytes");
    }

    @Test
    public void testImportExcel() throws IOException {
        // 首先生成一个模板
        ExcelConfig<Grade> config = ExcelConfigFactory.getGradeImportConfig();
        byte[] templateData = universalExcelService.generateTemplate(config);

        // 创建模拟文件
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test-grades.xlsx",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            templateData
        );

        // 测试导入（空模板）
        UniversalExcelService.ImportResult<Grade> result = universalExcelService.importExcel(file, config);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getSuccessData().size()); // 空模板应该没有数据
        System.out.println("Excel导入测试完成，结果: " + result.getMessage());
    }

    @Test
    public void testExcelConfigFactory() {
        // 测试成绩导入配置
        ExcelConfig<Grade> importConfig = ExcelConfigFactory.getGradeImportConfig();
        assertNotNull(importConfig);
        assertNotNull(importConfig.getHeaders());
        assertEquals("成绩导入", importConfig.getSheetName());

        // 测试成绩导出配置
        ExcelConfig<Object> exportConfig = ExcelConfigFactory.getGradeExportConfig();
        assertNotNull(exportConfig);
        assertNotNull(exportConfig.getHeaders());
        assertEquals("成绩导出", exportConfig.getSheetName());

        System.out.println("Excel配置工厂测试通过");
    }
}
